import { isValidElement, cloneElement, useRef, useState, ReactElement, useMemo, useCallback, useEffect, RefObject } from "react";
import useChildrenDimensions from "../useChildrenDimensions";

interface Offset {
  start: number;
  end: number;
}

interface RoulerConfig {
  position: 'left' | 'right' | 'top' | 'bottom';
  value: string;
  offset: Offset;
}

// 定义支持onChange回调的组件props类型
interface WithOnChangeProps {
  onChange?: (hidden: boolean) => void;
}

interface RoulerProps {
  vertical?: RoulerConfig;
  horizontal?: RoulerConfig;
  children: React.ReactNode;
}

// Rouler 首先就是单纯的一个容器组件
// 首先入参 vertical horizontal 两个方向的配置， children 
// 使用的时候就只需要传入一个 比如 vertical 就可以 ，然后传入 children 
// 然后我们根据传入的配置，来渲染出对应的样式即可
// 比如 vertical = { position: 'bottom', value: '100px', offset: { start: 48, end: 0 }}   就是尺度 显示在底部就是代表高度， 然后 value 就是高度的值， offset 就是起始和结束的偏移量
// 否则 horizontal = { position: 'right', value: '100px', offset: { start: 48, end: 0 }}   就是尺度 显示在右边就是代表宽度， 然后 value 就是宽度的值， offset 就是起始和结束的偏移量
// 布局的话如何让尺度线条放在 右边 或者 底部 都是相同的布局，只是方向不同而已 应该如何实现？ 
// 以及 Rouler 的位置如何根据 position 来确定呢？

export default function Rouler({ vertical, horizontal, children }: RoulerProps) {
  const childRef = useRef<HTMLDivElement>(null)
  const [isAddressBarHidden, setIsAddressBarHidden] = useState(false)
  const { width, height } = useChildrenDimensions(childRef as RefObject<HTMLDivElement>)

  const verticalStyle = useMemo(() => {
    if (!vertical) return null
    const { position, offset } = vertical
    const baseStyle: React.CSSProperties = { position: 'absolute' }

    switch (position) {
      case 'left':
        return {
          ...baseStyle,
          left: -4,
          top: isAddressBarHidden ? offset.end : offset.start,
          height: height - (isAddressBarHidden ? offset.end : offset.start),
          transform: 'translateX(-100%)',
          transition: 'top 0.3s ease-in-out, height 0.3s ease-in-out',
        }

      case 'right':
        return {
          ...baseStyle,
          right: -4,
          top: isAddressBarHidden ? offset.end : offset.start,
          height: height - (isAddressBarHidden ? offset.end : offset.start),
          transform: 'translateX(100%)',
          transition: 'top 0.3s ease-in-out, height 0.3s ease-in-out',
        }

      default:
        return baseStyle
    }
  }, [vertical, isAddressBarHidden, height])

  const horizontalStyle = useMemo(() => {
    if (!horizontal) return null
    const { position } = horizontal
    const baseStyle: React.CSSProperties = { position: 'absolute' }

    switch (position) {
      case 'top':
        return {
          ...baseStyle,
          top: -4,
          left: 0,
          transform: 'translateY(-100%)',
        }

      case 'bottom':
        return {
          ...baseStyle,
          bottom: -4,
          left: 0,
          transform: 'translateY(100%)',
        }

      default:
        return baseStyle
    }
  }, [horizontal])

  const renderVerticalRouler = useCallback((config: RoulerConfig, style: React.CSSProperties | null) => {
    const { position, value } = config

    if (position === 'left') {
      return (
        <div className="absolute left-0 top-0 border-t-1 border-b-1 h-full w-3" style={style || undefined}>
          <div className="absolute top-0 left-[50%] h-full border-l-1"></div>
          <div className="absolute top-[50%] right-[50%] translate-y-[50%] -translate-x-2 text-sm">{value}</div>
        </div>
      )
    }

    if (position === 'right') {
      return (
        <div className="absolute -right-5 top-0 border-t-1 border-b-1 h-full w-3" style={style || undefined}>
          <div className="absolute top-0 left-[50%] h-full border-l-1"></div>
          <div className="absolute top-[50%] left-[50%] translate-y-[50%] translate-x-2 text-sm">{value}</div>
        </div>
      )
    }

    return null
  }, [])

  const renderHorizontalRouler = useCallback((config: RoulerConfig, style: React.CSSProperties | null) => {
    const { position, value } = config

    if (position === 'top') {
      return (
        <div className="absolute left-0 top-0 border-l-1 border-r-1 w-full h-3" style={style || undefined}>
          <div className="absolute left-0 top-[50%] w-full border-t-1"></div>
          <div className="absolute left-[50%] bottom-[50%] -translate-x-[50%] -translate-y-2 text-sm">{value}</div>
        </div>
      )
    }

    if (position === 'bottom') {
      return (
        <div className="absolute left-0 -bottom-5 border-l-1 border-r-1 w-full h-3" style={style || undefined}>
          <div className="absolute left-0 top-[50%] w-full border-t-1"></div>
          <div className="absolute left-[50%] top-[50%] -translate-x-[50%] translate-y-2 text-sm">{value}</div>
        </div>
      )
    }

    return null
  }, [])

  const handleAddressBarChange = useCallback((hidden: boolean) => setIsAddressBarHidden(hidden), [])

  const renderChildren = useCallback(() => {
    if (!isValidElement(children)) {
      return <div ref={childRef}>{children}</div>;
    }

    const childElement = children as ReactElement<WithOnChangeProps & { ref?: React.Ref<any> }>;

     const childWithRef = typeof childElement.type !== 'string' && !childElement.props.onChange
      ? cloneElement(childElement, {
        ref: childRef,
        onChange: handleAddressBarChange
      })
      : cloneElement(childElement, { ref: childRef });

    return childWithRef;
  }, [children, handleAddressBarChange]);

  return (
    <div className="relative m-auto" style={{ width, height }}>
      {renderChildren()}
      {vertical && renderVerticalRouler(vertical, verticalStyle)}
      {horizontal && renderHorizontalRouler(horizontal, horizontalStyle)}
    </div>
  )
}
