import { isValidElement, cloneElement, useRef, useState, ReactElement, useMemo, useCallback, useEffect, RefObject } from "react";
import useChildrenDimensions from "../useChildrenDimensions";

interface Offset {
  start: number;
  end: number;
}

interface RoulerConfig {
  position: 'left' | 'right' | 'top' | 'bottom';
  value: string;
  offset: Offset;
}

interface WithOnChangeProps {
  onChange?: (hidden: boolean) => void;
}

interface RoulerProps {
  vertical?: RoulerConfig;
  horizontal?: RoulerConfig;
  children: React.ReactNode;
}

export default function Rouler({ vertical, horizontal, children }: RoulerProps) {
  const childRef = useRef<HTMLDivElement>(null)
  const [isAddressBarHidden, setIsAddressBarHidden] = useState(false)
  const { width, height } = useChildrenDimensions(childRef as RefObject<HTMLDivElement>)

  const verticalStyle = useMemo(() => {
    if (!vertical) return null
    const { position, offset } = vertical

    switch (position) {
      case 'left':
        return {
          left: -4,
          bottom: 0,
          top: isAddressBarHidden ? offset.end : offset.start,
          height: height - (isAddressBarHidden ? offset.end : offset.start),
          transform: 'translateX(-100%)',
          
        }

      case 'right':
        return {
          right: -4,
          bottom: 0,
          top: isAddressBarHidden ? offset.end : offset.start,
          height: height - (isAddressBarHidden ? offset.end : offset.start),
          transform: 'translateX(100%)',
        }

      default:
        return null
    }
  }, [vertical, isAddressBarHidden, height])

  const horizontalStyle = useMemo(() => {
    if (!horizontal) return null
    const { position } = horizontal

    switch (position) {
      case 'top':
        return {
          top: -4,
          left: 0,
          width: width,
          transform: 'translateY(-100%)',
        }

      case 'bottom':
        return {
          bottom: -4,
          left: 0,
          width: width,
          transform: 'translateY(100%)',
        }

      default:
        return null
    }
  }, [horizontal, width])

  const renderVerticalRouler = useCallback((config: RoulerConfig, style: React.CSSProperties | null) => {
    const { position, value } = config

    if (position === 'left') {
      return (
        <div className="absolute left-0 top-0 border-t-1 border-b-1 h-full w-3" style={style || undefined}>
          <div className="absolute top-0 left-[50%] h-full border-l-1"></div>
          <div className="absolute top-[50%] right-[50%] translate-y-[50%] -translate-x-2 text-sm">{value}</div>
        </div>
      )
    }

    if (position === 'right') {
      return (
        <div className="absolute -right-5 top-0 border-t-1 border-b-1 h-full w-3" style={style || undefined}>
          <div className="absolute top-0 left-[50%] h-full border-l-1"></div>
          <div className="absolute top-[50%] left-[50%] translate-y-[50%] translate-x-2 text-sm">{value}</div>
        </div>
      )
    }

    return null
  }, [])

  const renderHorizontalRouler = useCallback((config: RoulerConfig, style: React.CSSProperties | null) => {
    const { position, value } = config

    if (position === 'top') {
      return (
        <div className="absolute left-0 top-0 border-l-1 border-r-1 w-full h-3" style={style || undefined}>
          <div className="absolute left-0 top-[50%] w-full border-t-1"></div>
          <div className="absolute left-[50%] bottom-[50%] -translate-x-[50%] -translate-y-2 text-sm">{value}</div>
        </div>
      )
    }

    if (position === 'bottom') {
      return (
        <div className="absolute left-0 -bottom-5 border-l-1 border-r-1 w-full h-3" style={style || undefined}>
          <div className="absolute left-0 top-[50%] w-full border-t-1"></div>
          <div className="absolute left-[50%] top-[50%] -translate-x-[50%] translate-y-2 text-sm">{value}</div>
        </div>
      )
    }

    return null
  }, [])

  const handleAddressBarChange = useCallback((hidden: boolean) => setIsAddressBarHidden(hidden), [])

  const renderChildren = useCallback(() => {
    if (!isValidElement(children)) { return children }

    const childElement = children as ReactElement<WithOnChangeProps & { ref?: React.Ref<any> }>;

    const childWithRef = typeof childElement.type !== 'string' && !childElement.props.onChange
      ? cloneElement(childElement, {
        ref: childRef,
        onChange: handleAddressBarChange
      })
      : cloneElement(childElement, { ref: childRef });

    return childWithRef;
  }, [children, handleAddressBarChange]);

  return (
    <div className="relative" style={{ width, height }}>
      {renderChildren()}
      {vertical && renderVerticalRouler(vertical, verticalStyle)}
      {horizontal && renderHorizontalRouler(horizontal, horizontalStyle)}
    </div>
  )
}
